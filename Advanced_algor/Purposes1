核心目标：用python做一个量化交易中国A股，或者期货，或现货，或者外汇的自动化交易系统，请帮我实现这个系统，并给出详细的说明和代码。

这个交易的系统的模块如下：
1. 数据获取和数据处理模块
1.1 每天自动从yahoo财经等或者合适的股票数据网站获取我要的股票数据。
1.2 用tushare或者合适的python包获取某只科技行业头部公司股票数据。要尽可能的下载这个股票的全部每日历史数据，
1.3 自动检查下载下来的数据是否合适，是否正确，是否有n/a或者异常，用图表和数据参数提示我调试喂入模型或强化学习模型训练的数据是否需要修改/删减。

2. 模型训练模块
2.1 用以上数据模块和调试好的数据进行训练，将数据分为前80%训练集和后20%预测集。
2.2 要用机器学习方法学习某只股票的历史数据，预测未来一天股票是涨是跌，并且预测这只股票当日涨跌后的价格。
2.3 用强化学习方法，学习每天股票历史数据，接下来一天股票是涨是跌，并且预测这只股票当日涨跌后的价格。
2.4 调用多种模型，定义一个main函数调用各种模型，用tensorflow的机器学习架构，并比较不同模型的效果。
2.5 调用多种数据分析工具，比如数据清洗，数据预处理，数据分析，数据可视化等。

3. 交易模块
3.1 当模型训练好后，我需要你结合训练好的所有模型给出我判断，判断出明天这只股票是涨是跌，并且预测这只股票当日涨跌后的价格，然后自动帮我在交易平台买入或卖出一定量的股票。
3.2 建立一个自动发送email的程序，当我的股票盈利后，自动发送email给我，显示我买该股票的最初本金，今日盈利现金量（如果今日亏损则写负号），假设交易费是买卖股票数额的5%。并且在email中附上关于该只科技股票的各种最新新闻报告。

告诉我每一步，解释每一步，并给出详细的代码。用中文注释每一行代码。解释放在每行代码后面。
在我的MLQuant文件夹里分别安排不同python文件，执行以上3个模块。

我需要你详细、充分标注你最终生成的这3份python文件里的每一行代码，解释"每一行"代码的意思，当代码检查员是一个代码初学者，小白，用"井"这个符号在每行代码后充分解释代码的意思。因此你每个python文件每行后面都要加注释，有些python包，要详细告诉我意思，有些运算或代码需要告诉我你为何这样使用，为何这样打，因为我是很初级的python和金融股票初学者。






---------------------------

在MLQuant这个问价家中，将以下代码生成一个测试代码包，命名为"TestPack"
# 导入必要的库
import yfinance as yf
import tushare as ts
import pandas as pd
import matplotlib.pyplot as plt


import tushare as ts
ts.set_token('fc3a8e9a683a6d363f2f252b2e06ef0516cc5f5e94bf49acdc50669e')
pro = ts.pro_api()
# 尝试获取数据（例如贵州茅台的日线数据）
data = pro.daily(ts_code='600519.SH', start_date='20230101', end_date='20230201')
print(data.head())